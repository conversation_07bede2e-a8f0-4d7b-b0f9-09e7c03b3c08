const WebSocket = require('ws');
const mysql = require('mysql2/promise');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const cron = require('node-cron');
require('dotenv').config();

class CoinScoutWebSocketServer {
    constructor() {
        this.wss = null;
        this.db = null;
        this.clients = new Map(); // userId -> Set of WebSocket connections
        this.connectionUsers = new Map(); // WebSocket -> userId

        this.init();
    }

    async init() {
        try {
            // Database connection
            await this.connectDatabase();

            // WebSocket server
            this.createWebSocketServer();

            // Start alert checker
            this.startAlertChecker();

            console.log('🚀 CoinScout WebSocket Server started successfully');
        } catch (error) {
            console.error('❌ Failed to start server:', error);
            process.exit(1);
        }
    }

    async connectDatabase() {
        this.db = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME,
            port: process.env.DB_PORT || 3306
        });

        console.log('✅ Database connected');
    }

    createWebSocketServer() {
        const port = process.env.WS_PORT || 8080;
        const host = process.env.WS_HOST || '0.0.0.0';

        this.wss = new WebSocket.Server({
            port: port,
            host: host
        });

        this.wss.on('connection', (ws, req) => {
            console.log('🔗 New WebSocket connection');

            ws.on('message', async (message) => {
                try {
                    const data = JSON.parse(message);
                    await this.handleMessage(ws, data);
                } catch (error) {
                    console.error('❌ Error handling message:', error);
                    this.sendError(ws, 'Invalid message format');
                }
            });

            ws.on('close', () => {
                this.handleDisconnection(ws);
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket error:', error);
                this.handleDisconnection(ws);
            });
        });

        console.log(`🌐 WebSocket server listening on ${host}:${port}`);
    }

    async handleMessage(ws, data) {
        switch (data.type) {
            case 'auth':
                await this.handleAuthentication(ws, data);
                break;
            case 'ping':
                this.sendMessage(ws, { type: 'pong' });
                break;
            default:
                this.sendError(ws, 'Unknown message type');
        }
    }

    async handleAuthentication(ws, data) {
        try {
            if (!data.token) {
                this.sendError(ws, 'Token required');
                return;
            }

            // Verify JWT token
            const decoded = jwt.verify(data.token, process.env.JWT_SECRET);
            const userId = decoded.userId;

            if (!userId) {
                this.sendError(ws, 'Invalid token');
                return;
            }

            // Store user connection
            this.connectionUsers.set(ws, userId);

            if (!this.clients.has(userId)) {
                this.clients.set(userId, new Set());
            }
            this.clients.get(userId).add(ws);

            // Send authentication success
            this.sendMessage(ws, {
                type: 'auth_success',
                message: 'Authentication successful',
                userId: userId
            });

            console.log(`✅ User ${userId} authenticated, sending unread notifications...`);

            // Send unread notifications
            await this.sendUnreadNotifications(userId, ws);

            console.log(`✅ User ${userId} authentication process completed`);
        } catch (error) {
            console.error('❌ Authentication error:', error);
            this.sendError(ws, 'Authentication failed');
        }
    }

    handleDisconnection(ws) {
        const userId = this.connectionUsers.get(ws);
        if (userId) {
            const userConnections = this.clients.get(userId);
            if (userConnections) {
                userConnections.delete(ws);
                if (userConnections.size === 0) {
                    this.clients.delete(userId);
                }
            }
            this.connectionUsers.delete(ws);
            console.log(`🔌 User ${userId} disconnected`);
        }
    }

    sendMessage(ws, message) {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        }
    }

    sendError(ws, error) {
        this.sendMessage(ws, {
            type: 'error',
            message: error
        });
    }

    async sendUnreadNotifications(userId, ws = null) {
        try {
            console.log(`🔍 Checking unread notifications for user ${userId}...`);

            const [rows] = await this.db.execute(`
                SELECT id, title, message, type, priority, coin_id, link, data, created_at
                FROM user_notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
            `, [userId]);

            console.log(`📊 Found ${rows.length} unread notifications for user ${userId}`);

            const notifications = rows.map(row => {
                let parsedData = null;
                if (row.data) {
                    try {
                        parsedData = JSON.parse(row.data);
                    } catch (error) {
                        console.error(`❌ Invalid JSON in notification ${row.id} data:`, row.data);
                        console.error(`❌ JSON parse error:`, error.message);
                        parsedData = null;
                    }
                }

                return {
                    id: row.id,
                    title: row.title,
                    message: row.message,
                    type: row.type,
                    priority: row.priority,
                    coinId: row.coin_id,
                    link: row.link,
                    data: parsedData,
                    timestamp: row.created_at
                };
            });

            const message = {
                type: 'unread_notifications',
                notifications: notifications,
                count: notifications.length
            };

            if (ws) {
                // Send to specific connection
                this.sendMessage(ws, message);
                console.log(`📬 Sent ${notifications.length} unread notifications to user ${userId} via specific connection`);
            } else {
                // Send to all user connections
                this.sendToUser(userId, message);
                console.log(`📬 Sent ${notifications.length} unread notifications to user ${userId} via all connections`);
            }
        } catch (error) {
            console.error('❌ Error sending unread notifications:', error);
            console.error('❌ Error details:', error.message);
            console.error('❌ Stack trace:', error.stack);
        }
    }

    sendToUser(userId, message) {
        const userConnections = this.clients.get(userId);
        if (userConnections) {
            userConnections.forEach(ws => {
                this.sendMessage(ws, message);
            });
        }
    }

    formatPrice(price) {
        const numPrice = parseFloat(price);

        if (numPrice >= 1) {
            // $1 ve üzeri için maksimum 2 ondalık
            return numPrice.toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            });
        } else if (numPrice >= 0.01) {
            // $0.01 - $0.99 arası için maksimum 4 ondalık
            return numPrice.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 4
            });
        } else {
            // $0.01'den küçük için maksimum 8 ondalık
            return numPrice.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 8
            });
        }
    }

    startAlertChecker() {
        // Run every 1 minute
        cron.schedule('*/1 * * * *', async () => {
            console.log('🔍 Checking alerts...');
            await this.checkAlerts();
        });

        console.log('⏰ Alert checker started (every 1 minute)');
    }

    async checkAlerts() {
        try {
            // Get active alerts
            const [alerts] = await this.db.execute(`
                SELECT
                    a.id,
                    a.user_id,
                    a.coin_id,
                    c.name AS coin_name,
                    c.symbol AS coin_symbol,
                    c.image AS coin_image,
                    c.geckoid,
                    a.alert_type,
                    a.condition_above,
                    a.condition_below,
                    a.threshold_above,
                    a.threshold_below,
                    a.price_above,
                    a.price_below,
                    a.is_triggered_above,
                    a.is_triggered_below
                FROM user_alerts a
                JOIN coindata c ON a.coin_id = c.id
                WHERE a.is_active = 1
                AND (
                    (a.condition_above = 1 AND a.is_triggered_above = 0) OR
                    (a.condition_below = 1 AND a.is_triggered_below = 0)
                )
            `);

            if (alerts.length === 0) {
                console.log('📭 No active alerts to check');
                return;
            }

            // Group alerts by type
            const priceAlerts = alerts.filter(alert => alert.alert_type === 'price');
            const aiScoreAlerts = alerts.filter(alert => alert.alert_type === 'ai-score');

            // Check price alerts
            if (priceAlerts.length > 0) {
                await this.checkPriceAlerts(priceAlerts);
            }

            // Check AI score alerts
            if (aiScoreAlerts.length > 0) {
                await this.checkAiScoreAlerts(aiScoreAlerts);
            }

        } catch (error) {
            console.error('❌ Error checking alerts:', error);
        }
    }

    async checkPriceAlerts(alerts) {
        try {
            // Get unique gecko IDs
            const geckoIds = [...new Set(alerts.map(alert => alert.geckoid).filter(Boolean))];

            if (geckoIds.length === 0) {
                console.log('⚠️ No valid gecko IDs for price alerts');
                return;
            }

            // Fetch current prices from CoinGecko
            const prices = await this.fetchCurrentPrices(geckoIds);

            for (const alert of alerts) {
                if (!alert.geckoid || !prices[alert.geckoid]) {
                    continue;
                }

                const currentPrice = prices[alert.geckoid];

                // Check above condition
                if (alert.condition_above && !alert.is_triggered_above && currentPrice >= alert.price_above) {
                    await this.triggerAlert(alert, {
                        condition: 'above',
                        threshold: alert.price_above,
                        currentValue: currentPrice,
                        message: `${alert.coin_name} price above $${this.formatPrice(alert.price_above)}: $${this.formatPrice(currentPrice)}`
                    });
                }

                // Check below condition
                if (alert.condition_below && !alert.is_triggered_below && currentPrice <= alert.price_below) {
                    await this.triggerAlert(alert, {
                        condition: 'below',
                        threshold: alert.price_below,
                        currentValue: currentPrice,
                        message: `${alert.coin_name} price below $${this.formatPrice(alert.price_below)}: $${this.formatPrice(currentPrice)}`
                    });
                }
            }
        } catch (error) {
            console.error('❌ Error checking price alerts:', error);
        }
    }

    async checkAiScoreAlerts(alerts) {
        try {
            for (const alert of alerts) {
                // Fetch current AI score for the coin
                const currentScore = await this.fetchCurrentAiScore(alert.coin_id);

                if (currentScore === null) {
                    continue;
                }

                // Check above condition
                if (alert.condition_above && !alert.is_triggered_above && currentScore >= alert.threshold_above) {
                    await this.triggerAlert(alert, {
                        condition: 'above',
                        threshold: alert.threshold_above,
                        currentValue: currentScore,
                        message: `${alert.coin_name} AI score above ${parseFloat(alert.threshold_above).toFixed(1)}: ${parseFloat(currentScore).toFixed(1)}`
                    });
                }

                // Check below condition
                if (alert.condition_below && !alert.is_triggered_below && currentScore <= alert.threshold_below) {
                    await this.triggerAlert(alert, {
                        condition: 'below',
                        threshold: alert.threshold_below,
                        currentValue: currentScore,
                        message: `${alert.coin_name} AI score below ${parseFloat(alert.threshold_below).toFixed(1)}: ${parseFloat(currentScore).toFixed(1)}`
                    });
                }
            }
        } catch (error) {
            console.error('❌ Error checking AI score alerts:', error);
        }
    }

    async fetchCurrentPrices(geckoIds) {
        try {
            const url = `${process.env.COINGECKO_API_URL || 'https://api.coingecko.com/api/v3'}/simple/price`;
            const response = await axios.get(url, {
                params: {
                    ids: geckoIds.join(','),
                    vs_currencies: 'usd'
                }
            });

            const prices = {};
            for (const [geckoId, data] of Object.entries(response.data)) {
                prices[geckoId] = data.usd;
            }

            return prices;
        } catch (error) {
            console.error('❌ Error fetching prices:', error);
            return {};
        }
    }

    async fetchCurrentAiScore(coinId) {
        try {
            // Fetch from your database - adjust query as needed
            const [rows] = await this.db.execute(`
                SELECT total_score
                FROM coindata
                WHERE id = ?
                LIMIT 1
            `, [coinId]);

            return rows.length > 0 ? parseFloat(rows[0].total_score) : null;
        } catch (error) {
            console.error('❌ Error fetching AI score:', error);
            return null;
        }
    }

    async triggerAlert(alert, triggerData) {
        try {
            // Mark specific condition as triggered
            if (triggerData.condition === 'above') {
                await this.db.execute(`
                    UPDATE user_alerts
                    SET is_triggered_above = 1, triggered_above_at = NOW()
                    WHERE id = ?
                `, [alert.id]);
            } else if (triggerData.condition === 'below') {
                await this.db.execute(`
                    UPDATE user_alerts
                    SET is_triggered_below = 1, triggered_below_at = NOW()
                    WHERE id = ?
                `, [alert.id]);
            }

            // Create notification record
            const notificationData = {
                coinId: alert.coin_id,
                coinName: alert.coin_name,
                coinSymbol: alert.coin_symbol,
                coinImage: alert.coin_image,
                alertType: alert.alert_type,
                condition: triggerData.condition,
                threshold: triggerData.threshold,
                currentValue: triggerData.currentValue
            };

            const [result] = await this.db.execute(`
                INSERT INTO user_notifications
                (user_id, title, message, type, priority, coin_id, data, created_at, is_read)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), 0)
            `, [
                alert.user_id,
                `${alert.coin_name} Alert Triggered`,
                triggerData.message,
                alert.alert_type,
                'high',
                alert.coin_id,
                JSON.stringify(notificationData)
            ]);

            // Send real-time notification via WebSocket
            const notification = {
                id: result.insertId,
                title: `${alert.coin_name} Alert Triggered`,
                message: triggerData.message,
                type: alert.alert_type,
                priority: 'high',
                coinId: alert.coin_id,
                data: notificationData,
                timestamp: new Date().toISOString()
            };

            this.sendToUser(alert.user_id, {
                type: 'new_notification',
                notification: notification
            });

            console.log(`🚨 Alert triggered for user ${alert.user_id}: ${triggerData.message}`);
        } catch (error) {
            console.error('❌ Error triggering alert:', error);
        }
    }
}

// Start the server
const server = new CoinScoutWebSocketServer();

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down WebSocket server...');

    if (server.wss) {
        server.wss.close(() => {
            console.log('✅ WebSocket server closed');
        });
    }

    if (server.db) {
        await server.db.end();
        console.log('✅ Database connection closed');
    }

    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');

    if (server.wss) {
        server.wss.close(() => {
            console.log('✅ WebSocket server closed');
        });
    }

    if (server.db) {
        await server.db.end();
        console.log('✅ Database connection closed');
    }

    process.exit(0);
});
