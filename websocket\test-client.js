/**
 * Test Client for CoinScout WebSocket Server
 * Node.js test script to verify WebSocket functionality
 */

const WebSocket = require('ws');
const jwt = require('jsonwebtoken');

// Test configuration
const WS_URL = 'ws://localhost:8080';
const JWT_SECRET = '48Scw74aAgf16wvAhhr85411A18w3';
const TEST_USER_ID = 1; // Change this to a valid user ID in your database

// Create a test JWT token
function createTestToken(userId) {
    const payload = {
        userId: userId,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
    };
    
    return jwt.sign(payload, JWT_SECRET);
}

class WebSocketTestClient {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.testToken = createTestToken(TEST_USER_ID);
    }

    connect() {
        console.log('🔗 Connecting to WebSocket server...');
        console.log('📍 URL:', WS_URL);
        console.log('👤 Test User ID:', TEST_USER_ID);
        console.log('🔑 Test Token:', this.testToken.substring(0, 50) + '...');
        
        this.ws = new WebSocket(WS_URL);
        
        this.ws.on('open', () => {
            console.log('✅ Connected to WebSocket server');
            this.isConnected = true;
            this.authenticate();
        });
        
        this.ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                this.handleMessage(message);
            } catch (error) {
                console.error('❌ Error parsing message:', error);
            }
        });
        
        this.ws.on('close', (code, reason) => {
            console.log('🔌 Disconnected from WebSocket server');
            console.log('📊 Close code:', code);
            console.log('📝 Reason:', reason.toString());
            this.isConnected = false;
        });
        
        this.ws.on('error', (error) => {
            console.error('❌ WebSocket error:', error);
        });
    }

    authenticate() {
        console.log('🔐 Sending authentication...');
        this.send({
            type: 'auth',
            token: this.testToken
        });
    }

    handleMessage(message) {
        console.log('📨 Received message:', JSON.stringify(message, null, 2));
        
        switch (message.type) {
            case 'auth_success':
                console.log('✅ Authentication successful!');
                console.log('👤 User ID:', message.userId);
                this.startTests();
                break;
                
            case 'unread_notifications':
                console.log('📬 Unread notifications received:');
                console.log('📊 Count:', message.count);
                if (message.notifications && message.notifications.length > 0) {
                    message.notifications.forEach((notif, index) => {
                        console.log(`  ${index + 1}. ${notif.title} - ${notif.message}`);
                    });
                }
                break;
                
            case 'new_notification':
                console.log('🔔 New notification received:');
                console.log('📋 Title:', message.notification.title);
                console.log('💬 Message:', message.notification.message);
                console.log('🏷️ Type:', message.notification.type);
                break;
                
            case 'error':
                console.error('❌ Server error:', message.message);
                break;
                
            case 'pong':
                console.log('💓 Pong received');
                break;
                
            default:
                console.log('❓ Unknown message type:', message.type);
        }
    }

    send(data) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
            console.log('📤 Sent:', JSON.stringify(data, null, 2));
        } else {
            console.error('⚠️ Cannot send - not connected');
        }
    }

    startTests() {
        console.log('\n🧪 Starting tests...\n');
        
        // Test 1: Send ping
        setTimeout(() => {
            console.log('🧪 Test 1: Sending ping...');
            this.send({ type: 'ping' });
        }, 1000);
        
        // Test 2: Send invalid message
        setTimeout(() => {
            console.log('🧪 Test 2: Sending invalid message...');
            this.send({ type: 'invalid_type' });
        }, 3000);
        
        // Test 3: Keep connection alive for a while
        setTimeout(() => {
            console.log('🧪 Test 3: Keeping connection alive for alert testing...');
            console.log('💡 You can now trigger alerts in your database to test real-time notifications');
            console.log('💡 The client will stay connected for 5 minutes');
        }, 5000);
        
        // Auto-disconnect after 5 minutes
        setTimeout(() => {
            console.log('⏰ Test completed - disconnecting...');
            this.disconnect();
        }, 300000); // 5 minutes
    }

    disconnect() {
        if (this.ws) {
            this.ws.close(1000, 'Test completed');
        }
    }
}

// Run the test
console.log('🚀 Starting CoinScout WebSocket Test Client\n');

const testClient = new WebSocketTestClient();
testClient.connect();

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT - disconnecting...');
    testClient.disconnect();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM - disconnecting...');
    testClient.disconnect();
    process.exit(0);
});
