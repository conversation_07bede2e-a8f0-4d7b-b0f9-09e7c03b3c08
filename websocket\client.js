/**
 * CoinScout WebSocket Client
 * Frontend JavaScript client for real-time notifications
 */
class CoinScoutWebSocketClient {
    constructor(wsUrl, authToken, options = {}) {
        this.wsUrl = wsUrl;
        this.authToken = authToken;
        this.options = {
            debug: false,
            reconnectInterval: 5000,
            maxReconnectAttempts: 10,
            ...options
        };
        
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.eventHandlers = {};
        this.heartbeatInterval = null;
        
        this.connect();
    }

    connect() {
        try {
            this.log('🔗 Connecting to WebSocket server...');
            this.ws = new WebSocket(this.wsUrl);
            
            this.ws.onopen = () => this.handleOpen();
            this.ws.onmessage = (event) => this.handleMessage(event);
            this.ws.onclose = (event) => this.handleClose(event);
            this.ws.onerror = (error) => this.handleError(error);
            
        } catch (error) {
            this.log('❌ Connection error:', error);
            this.scheduleReconnect();
        }
    }

    handleOpen() {
        this.log('✅ WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // Authenticate
        this.authenticate();
        
        // Start heartbeat
        this.startHeartbeat();
        
        // Emit connected event
        this.emit('connected');
    }

    handleMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.log('📨 Received message:', data);
            
            switch (data.type) {
                case 'auth_success':
                    this.log('🔐 Authentication successful');
                    this.emit('authenticated', data);
                    break;
                    
                case 'new_notification':
                    this.log('🔔 New notification received');
                    this.emit('notification', data.notification);
                    break;
                    
                case 'unread_notifications':
                    this.log(`📬 ${data.count} unread notifications received`);
                    this.emit('unread_notifications', data);
                    break;
                    
                case 'pong':
                    this.log('💓 Heartbeat pong received');
                    break;
                    
                case 'error':
                    this.log('❌ Server error:', data.message);
                    this.emit('error', data.message);
                    break;
                    
                default:
                    this.log('❓ Unknown message type:', data.type);
            }
        } catch (error) {
            this.log('❌ Error parsing message:', error);
        }
    }

    handleClose(event) {
        this.log('🔌 WebSocket disconnected:', event.code, event.reason);
        this.isConnected = false;
        this.stopHeartbeat();
        
        this.emit('disconnected', { code: event.code, reason: event.reason });
        
        // Attempt to reconnect if not a clean close
        if (event.code !== 1000) {
            this.scheduleReconnect();
        }
    }

    handleError(error) {
        this.log('❌ WebSocket error:', error);
        this.emit('error', error);
    }

    authenticate() {
        if (this.isConnected && this.authToken) {
            this.send({
                type: 'auth',
                token: this.authToken
            });
        }
    }

    send(data) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
            this.log('📤 Sent message:', data);
        } else {
            this.log('⚠️ Cannot send message - not connected');
        }
    }

    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({ type: 'ping' });
            }
        }, 30000); // 30 seconds
    }

    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    scheduleReconnect() {
        if (this.reconnectAttempts < this.options.maxReconnectAttempts) {
            this.reconnectAttempts++;
            this.log(`🔄 Scheduling reconnect attempt ${this.reconnectAttempts}/${this.options.maxReconnectAttempts} in ${this.options.reconnectInterval}ms`);
            
            setTimeout(() => {
                this.connect();
            }, this.options.reconnectInterval);
        } else {
            this.log('❌ Max reconnect attempts reached');
            this.emit('max_reconnect_attempts_reached');
        }
    }

    // Event system
    on(event, handler) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(handler);
    }

    off(event, handler) {
        if (this.eventHandlers[event]) {
            const index = this.eventHandlers[event].indexOf(handler);
            if (index > -1) {
                this.eventHandlers[event].splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    this.log('❌ Error in event handler:', error);
                }
            });
        }
    }

    // Update auth token (for token refresh)
    updateAuthToken(newToken) {
        this.authToken = newToken;
        if (this.isConnected) {
            this.authenticate();
        }
    }

    // Disconnect
    disconnect() {
        this.log('🔌 Manually disconnecting...');
        this.stopHeartbeat();
        
        if (this.ws) {
            this.ws.close(1000, 'Manual disconnect');
        }
    }

    // Logging
    log(...args) {
        if (this.options.debug) {
            console.log('[CoinScout WebSocket]', ...args);
        }
    }
}

// Export for use in different environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CoinScoutWebSocketClient;
} else if (typeof window !== 'undefined') {
    window.CoinScoutWebSocketClient = CoinScoutWebSocketClient;
}
