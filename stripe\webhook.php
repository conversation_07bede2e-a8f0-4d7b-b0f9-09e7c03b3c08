
<?php
/**
 * Stripe Webhook Functions
 *
 * This file contains functions related to Stripe webhooks.
 */

// Include required files if not already included
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../secrets.php';
require_once __DIR__ . '/StripeLogger.php';

// StripeLogger sınıfı otomatik olarak yapılandırılmıştır
// Yapılandırma ayarları StripeLogger.php dosyasında bulunmaktadır

// Set Stripe API key
\Stripe\Stripe::setApiKey($stripeSecretKey);

/**
 * Handle webhook events from Stripe
 */
function handleWebhook() {
    global $link;

    // Get the webhook payload
    $payload = @file_get_contents('php://input');
    $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];

    try {
        // Verify webhook signature
        $event = \Stripe\Webhook::constructEvent(
            $payload, $sig_header, STRIPE_WEBHOOK_SECRET
        );

        // Log webhook event
        StripeLogger::logStripeEvent($event->type, $event->id, "Webhook event received");

        // Handle different event types
        switch ($event->type) {
            // Checkout session events
            case 'checkout.session.completed':
                handleCheckoutSessionCompleted($event->data->object);
                break;

            // Customer events
            case 'customer.created':
                handleNewCustomer($event->data->object);
                break;
            case 'customer.updated':
                handleUpdatedCustomer($event->data->object);
                break;

            // Subscription events
            case 'customer.subscription.created':
                handleSubscriptionCreated($event->data->object);
                break;
            case 'customer.subscription.updated':
                handleSubscriptionUpdated($event->data->object, $event);
                break;
            case 'customer.subscription.deleted':
                handleCanceledSubscription($event->data->object);
                break;

            // Payment events
            case 'payment_intent.succeeded':
                handleSuccessfulPayment($event->data->object);
                break;
            case 'payment_intent.payment_failed':
                handleFailedPayment($event->data->object);
                break;

            // Invoice events
            case 'invoice.paid':
                handleSuccessfulInvoice($event->data->object);
                break;
            case 'invoice.payment_failed':
                handleFailedInvoice($event->data->object);
                break;
            case 'invoice.updated':
                handleInvoiceUpdated($event->data->object);
                break;

            // Product events
            case 'product.created':
                handleProductCreated($event->data->object);
                break;
            case 'product.updated':
                handleProductUpdated($event->data->object);
                break;
            case 'product.deleted':
                handleProductDeleted($event->data->object);
                break;

            // Price events
            case 'price.created':
                handlePriceCreated($event->data->object);
                break;
            case 'price.updated':
                handlePriceUpdated($event->data->object);
                break;
            case 'price.deleted':
                handlePriceDeleted($event->data->object);
                break;

            // Payment method events
            case 'payment_method.attached':
                handlePaymentMethodAttached($event->data->object);
                break;
            case 'payment_method.detached':
                handlePaymentMethodDetached($event->data->object);
                break;

            // Discount and coupon events
            case 'customer.discount.created':
                handleCustomerDiscountCreated($event->data->object);
                break;
            case 'customer.discount.updated':
                handleCustomerDiscountUpdated($event->data->object);
                break;
            case 'customer.discount.deleted':
                handleCustomerDiscountDeleted($event->data->object);
                break;
            case 'coupon.created':
                handleCouponCreated($event->data->object);
                break;
            case 'coupon.updated':
                handleCouponUpdated($event->data->object);
                break;
            case 'coupon.deleted':
                handleCouponDeleted($event->data->object);
                break;

            // Promotion code events
            case 'promotion_code.created':
                handlePromotionCodeCreated($event->data->object);
                break;
            case 'promotion_code.updated':
                handlePromotionCodeUpdated($event->data->object);
                break;
            case 'promotion_code.deleted':
                handlePromotionCodeDeleted($event->data->object);
                break;

            default:
                StripeLogger::log(StripeLogLevel::NOTICE, "Unhandled event type: {$event->type}");
        }

        // Return success response
        http_response_code(200);
        echo json_encode(['status' => 'success']);

    } catch (\UnexpectedValueException $e) {
        // Invalid payload
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE WEBHOOK ERROR - Invalid payload: " . $e->getMessage());
        http_response_code(400);
        echo json_encode(['error' => 'Invalid payload']);
        exit();
    } catch (\Stripe\Exception\SignatureVerificationException $e) {
        // Invalid signature
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE WEBHOOK ERROR - Invalid signature: " . $e->getMessage());
        http_response_code(400);
        echo json_encode(['error' => 'Invalid signature']);
        exit();
    } catch (Exception $e) {
        // Other errors
        StripeLogger::log(StripeLogLevel::CRITICAL, "STRIPE WEBHOOK ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'trace' => $e->getTraceAsString()
        ]);
        http_response_code(500);
        echo json_encode(['error' => 'Server error']);
        exit();
    }
}
