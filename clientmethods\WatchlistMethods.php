<?php

/**
 * Watchlist Methods
 * Contains methods for managing user watchlists and IDO watchlists
 */

function get_user_watchlists($userid)
{
    global $link;
    $userid = intval($userid);
    $query = "SELECT id, name, description, icon_id FROM watchlists WHERE user_id = $userid";
    $result = mysqli_query($link, $query);
    if ($result) {
        $watchlists = mysqli_fetch_all($result, MYSQLI_ASSOC);
        $all_watchlists = [];
        foreach ($watchlists as &$watchlist) {
            $watchlist_id = $watchlist['id'];
            $coin_query = "
            SELECT ccl.id, ccl.geckoid, ccl.name, ccl.image, ccl.symbol,
                   ccl.total_score, ccl.marketcap_rank, ccl.sevenDayChange,
                   ccl.tokenomicsScore, ccl.securityScore, ccl.socialScore,
                   ccl.marketScore, ccl.fundamentalsScore
            FROM watchlist_coins wc
            JOIN client_coin_list ccl ON wc.coin_id = ccl.id
            WHERE wc.watchlist_id = $watchlist_id
            ";
            $rs = mysqli_query($link, $coin_query);
            $coins = [];
            if ($rs) {
                $pos = 1;
                while ($obj2 = mysqli_fetch_assoc($rs)) {
                    $obj2['pos'] = $pos++;
                    $obj2['change'] = round(floatval($obj2['sevenDayChange']), 2);
                    // Score'ları float'a çeviriyoruz
                    $obj2['tokenomicsScore'] = floatval($obj2['tokenomicsScore']);
                    $obj2['securityScore'] = floatval($obj2['securityScore']);
                    $obj2['socialScore'] = floatval($obj2['socialScore']);
                    $obj2['marketScore'] = floatval($obj2['marketScore']);
                    $obj2['fundamentalsScore'] = floatval($obj2['fundamentalsScore']);
                    unset($obj2['sevenDayChange']); // Orijinal alanı kaldırıyoruz
                    $coins[] = $obj2;
                }
            }
            $watchlist['coins'] = $coins;
            $all_watchlists[] = $watchlist;
        }
        $response = new SuccessResult($all_watchlists);
        $response->send();
    } else {
        $response = new ErrorResult('Failed to retrieve watchlists.');
        $response->send(500);
    }
}

function add_to_watchlist($watchlist_id, $coin_id)
{
    global $link;
    $watchlist_id = intval($watchlist_id);
    $coin_id = intval($coin_id);
    $check_query = "SELECT * FROM watchlist_coins WHERE watchlist_id = $watchlist_id AND coin_id = $coin_id";
    $check_result = mysqli_query($link, $check_query);
    if ($check_result && mysqli_num_rows($check_result) > 0) {
        $delete_query = "DELETE FROM watchlist_coins WHERE watchlist_id = $watchlist_id AND coin_id = $coin_id";
        $delete_result = mysqli_query($link, $delete_query);
        if ($delete_result) {
            $response = new SuccessResult("Coin successfully removed from watchlist.");
            $response->send();
        } else {
            $error = mysqli_error($link);
            $response = new ErrorResult("Failed to remove coin from watchlist. Error: $error");
            $response->send(500);
        }
    } else {
        $insert_query = "INSERT INTO watchlist_coins (watchlist_id, coin_id) VALUES ($watchlist_id, $coin_id)";
        $insert_result = mysqli_query($link, $insert_query);
        if ($insert_result) {
            $response = new SuccessResult("Coin successfully added to watchlist.");
            $response->send();
        } else {
            $error = mysqli_error($link);
            $response = new ErrorResult("Failed to add coin to watchlist. Error: $error");
            $response->send(500);
        }
    }
}

function add_to_watchlist_v2($watchlist_id, $coin_id)
{
    global $link, $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    $watchlist_id = intval($watchlist_id);
    $coin_id = intval($coin_id);
    $insert_query = "INSERT INTO watchlist_coins (watchlist_id, coin_id) VALUES ($watchlist_id, $coin_id)";
    $insert_result = mysqli_query($link, $insert_query);
    if ($insert_result) {
        $response = new SuccessResult($clientMessages[$lang]['coin_added_to_watchlist']);
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult($clientMessages[$lang]['failed_to_add_coin'] . " Error: $error");
        $response->send(500);
    }
}

function remove_from_watchlist_v2($watchlist_id, $coin_id)
{
    global $link, $selectedLanguage, $clientMessages;
    $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
    $watchlist_id = intval($watchlist_id);
    $coin_id = intval($coin_id);
    $delete_query = "DELETE FROM watchlist_coins WHERE watchlist_id = $watchlist_id AND coin_id = $coin_id";
    $delete_result = mysqli_query($link, $delete_query);
    if ($delete_result) {
        $response = new SuccessResult($clientMessages[$lang]['coin_removed_from_watchlist']);
        $response->send();
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult($clientMessages[$lang]['failed_to_remove_coin'] . " Error: $error");
        $response->send(500);
    }
}

function add_new_watchlist($user_id, $watchlist_name, $iconid, $coin_id = null, $description = null)
{
    global $link;
    $user_id = intval($user_id);
    // İzin kontrolü - Kullanıcının daha fazla watchlist ekleyip ekleyemeyeceğini kontrol et
    if (!canUserAddMoreWatchlists($user_id)) {
        global $selectedLanguage, $clientMessages, $link;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
        // Kullanıcının abonelik seviyesini al
        $query = "SELECT subscription_level FROM users WHERE id = ?";
        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        $subscriptionLevel = $user['subscription_level'] ?? 'free';
        // Kullanıcının mevcut watchlist sayısını al
        $query = "SELECT COUNT(*) as count FROM watchlists WHERE user_id = ?";
        $stmt = mysqli_prepare($link, $query);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        $currentCount = $row['count'];
        // Abonelik seviyesine göre limit belirle
        $limit = 0;
        $nextLevel = '';
        if ($subscriptionLevel === 'free') {
            $limit = 1;
            $nextLevel = 'Basic';
        } else if ($subscriptionLevel === 'basic') {
            $limit = 3;
            $nextLevel = 'Advance';
        } else if ($subscriptionLevel === 'advance') {
            $limit = 10;
            $nextLevel = 'Premium';
        }
        $errorMessage = $clientMessages[$lang]['watchlist_limit_reached'] ??
            "You have reached your watchlist limit ($currentCount/$limit). Please upgrade to $nextLevel for more watchlists.";
        $response = new ErrorResult($errorMessage);
        $response->send(403);
        return;
    }
    $watchlist_name = mysqli_real_escape_string($link, $watchlist_name);
    // description NULL kontrolü
    // Eğer description null, undefined veya boş string ise NULL olarak ayarla
    if ($description === null || $description === '' || trim($description) === '') {
        $description = "NULL";
    } else {
        $description = "'" . mysqli_real_escape_string($link, $description) . "'";
    }
    // coin_id NULL veya boş string kontrolü
    if (isset($coin_id) && $coin_id !== null && $coin_id !== '') {
        $coin_id = intval($coin_id);
        $has_coin = true;
    } else {
        $coin_id = "NULL";
        $has_coin = false;
    }
    $check_query = "SELECT * FROM watchlists WHERE user_id = $user_id AND name = '$watchlist_name'";
    $check_result = mysqli_query($link, $check_query);
    if ($check_result && mysqli_num_rows($check_result) > 0) {
        global $selectedLanguage, $clientMessages;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
        $response = new ErrorResult($clientMessages[$lang]['watchlist_already_exists']);
        $response->send(400);
        return;
    }
    // Yeni watchlist ekle
    $insert_query = "INSERT INTO watchlists (user_id, name, icon_id, description) VALUES ($user_id, '$watchlist_name', $iconid, $description)";
    $insert_result = mysqli_query($link, $insert_query);
    if ($insert_result) {
        $watchlist_id = mysqli_insert_id($link); // Yeni eklenen watchlist'in ID'sini al
        // coin_id varsa ve geçerli bir değerse watchlist_coins tablosuna ekleme yap
        if ($has_coin) {
            $coin_insert_query = "INSERT INTO watchlist_coins (watchlist_id, coin_id) VALUES ($watchlist_id, $coin_id)";
            $coin_insert_result = mysqli_query($link, $coin_insert_query);
            if (!$coin_insert_result) {
                global $selectedLanguage, $clientMessages;
                $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
                $error = mysqli_error($link);
                $response = new ErrorResult($clientMessages[$lang]['watchlist_created_but_failed_to_add_coin'] . " Error: $error");
                $response->send(500);
                return;
            }
        }
        global $selectedLanguage, $clientMessages;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
        $response = new SuccessResult($clientMessages[$lang]['watchlist_added']);
        $response->send();
    } else {
        global $selectedLanguage, $clientMessages;
        $lang = isset($clientMessages[$selectedLanguage]) ? $selectedLanguage : 'en';
        $error = mysqli_error($link);
        $response = new ErrorResult($clientMessages[$lang]['failed_to_add_watchlist'] . " Error: $error");
        $response->send(500);
    }
}

function delete_watchlist($watchlist_id, $user_id)
{
    global $link;
    $user_id = intval($user_id);
    $watchlist_id = intval($watchlist_id);
    $delete_query = "DELETE FROM watchlists WHERE id = $watchlist_id AND user_id = $user_id";
    $delete_result = mysqli_query($link, $delete_query);
    if ($delete_result) {
        if (mysqli_affected_rows($link) > 0) {
            $response = new SuccessResult("Watchlist successfully deleted.");
            $response->send();
        } else {
            $response = new ErrorResult("No watchlist found for the given ID and user.");
            $response->send(404);
        }
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to delete watchlist. Error: $error");
        $response->send(500);
    }
}

/**
 * Edit a watchlist's name and description
 *
 * @param int $watchlist_id The ID of the watchlist
 * @param string $watchlist_name The new name of the watchlist
 * @param int $icon_id The ID of the icon
 * @param int $user_id The ID of the user
 * @param string|null $description Optional description of the watchlist
 * @return void
 */
function edit_watchlist($watchlist_id, $watchlist_name, $icon_id, $user_id, $description = null)
{
    global $link;
    $user_id = intval($user_id);
    $watchlist_id = intval($watchlist_id);
    $icon_id = intval($icon_id);
    $watchlist_name = mysqli_real_escape_string($link, $watchlist_name);
    // description NULL kontrolü
    // Eğer description null, undefined veya boş string ise NULL olarak ayarla
    if ($description === null || $description === '' || trim($description) === '') {
        $description = "NULL";
    } else {
        $description = "'" . mysqli_real_escape_string($link, $description) . "'";
    }
    $update_query = "UPDATE watchlists SET name = '$watchlist_name', icon_id = $icon_id, description = $description WHERE id = $watchlist_id AND user_id = $user_id";
    $update_result = mysqli_query($link, $update_query);
    if ($update_result) {
        if (mysqli_affected_rows($link) > 0) {
            $response = new SuccessResult("Watchlist successfully updated.");
            $response->send();
        } else {
            $response = new ErrorResult("No changes made or watchlist not found.");
            $response->send(404);
        }
    } else {
        $error = mysqli_error($link);
        $response = new ErrorResult("Failed to update watchlist. Error: $error");
        $response->send(500);
    }
}


